// Modern 2025 Navbar Component
import React, { useState, useEffect, useRef } from 'react';
import { NavLink } from 'react-router-dom';
import { <PERSON>u, <PERSON>, Sparkles, Zap } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import logo from "../assets/logo1.png";
import './animations.css';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeIndex, setActiveIndex] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const navRef = useRef(null);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    const handleMouseMove = (e) => {
      if (navRef.current) {
        const rect = navRef.current.getBoundingClientRect();
        setMousePosition({
          x: (e.clientX - rect.left) / rect.width,
          y: (e.clientY - rect.top) / rect.height
        });
      }
    };

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  const navLinkStyles = ({ isActive }) =>
    `relative px-5 py-2.5 transition-all duration-500 ease-out group font-medium tracking-wide
    ${isActive
      ? 'text-white font-semibold'
      : 'text-slate-300 hover:text-white'}
    hover:scale-105 rounded-xl`;

  const getNavBackground = () => {
    return isScrolled
      ? 'bg-slate-900/95 backdrop-blur-2xl shadow-2xl shadow-black/20 border-b border-white/10'
      : 'bg-slate-950/80 backdrop-blur-xl border-b border-white/5';
  };
  
  const handleMouseEnter = (index) => {
    setActiveIndex(index);
  };

  const handleMouseLeave = () => {
    setActiveIndex(null);
  };

  return (
    <motion.nav
      ref={navRef}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-700 ${getNavBackground()}`}
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      {/* Dynamic gradient overlay that follows mouse */}
      <div
        className="absolute inset-0 opacity-30 transition-opacity duration-500"
        style={{
          background: `radial-gradient(circle at ${mousePosition.x * 100}% ${mousePosition.y * 100}%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)`
        }}
      />

      {/* Animated border gradient */}
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400/50 to-transparent"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="flex items-center justify-between h-20">
          {/* Enhanced Logo with modern effects */}
          <motion.div
            className="flex items-center group"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="relative">
              {/* Logo glow effect */}
              <div className="absolute -inset-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <img
                src={logo}
                alt="IPTV Logo"
                className="h-12 w-auto relative z-10 filter drop-shadow-xl transition-all duration-500 group-hover:brightness-110"
              />
            </div>
            {/* Optional brand text */}
            <div className="ml-3 hidden sm:block">
              <span className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400">
                IPTV Pro
              </span>
            </div>
          </motion.div>
          
          {/* Enhanced Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-2">
            {[
              { to: "/", text: "Home", icon: "🏠" },
              { to: "/features", text: "Features", icon: "⚡" },
              { to: "/packages", text: "Packages", icon: "📦" },
              { to: "/faq", text: "FAQ", icon: "❓" },
              { to: "/contact", text: "Contact", icon: "📞" }
            ].map((link, index) => (
              <div
                key={link.to}
                className="relative group"
                onMouseEnter={() => handleMouseEnter(index)}
                onMouseLeave={handleMouseLeave}
              >
                {/* Background glow effect */}
                <div className="absolute -inset-2 bg-gradient-to-r from-blue-500/0 via-blue-500/20 to-purple-500/0 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

                <NavLink
                  to={link.to}
                  className={navLinkStyles}
                >
                  {({ isActive }) => (
                    <div className="relative flex items-center space-x-2">
                      {/* Glass morphism background */}
                      <div className={`absolute inset-0 rounded-xl transition-all duration-500 ${
                        isActive
                          ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-400/30'
                          : 'bg-white/5 border border-white/10 group-hover:bg-white/10 group-hover:border-white/20'
                      }`}></div>

                      {/* Content */}
                      <span className="relative z-10 text-sm">{link.icon}</span>
                      <span className="relative z-10">{link.text}</span>

                      {/* Active indicator */}
                      {isActive && (
                        <motion.div
                          className="absolute -bottom-3 left-1/2 w-1 h-1 bg-blue-400 rounded-full"
                          initial={{ scale: 0, x: '-50%' }}
                          animate={{ scale: 1, x: '-50%' }}
                          transition={{ duration: 0.3 }}
                        />
                      )}

                      {/* Hover underline */}
                      <motion.span
                        className="absolute inset-x-2 -bottom-2 h-0.5 bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 origin-left rounded-full"
                        initial={{ scaleX: 0 }}
                        animate={{ scaleX: activeIndex === index && !isActive ? 1 : 0 }}
                        transition={{ duration: 0.4, ease: "easeOut" }}
                      />
                    </div>
                  )}
                </NavLink>
              </div>
            ))}
          </div>

          {/* CTA Button */}
          <div className="hidden lg:block">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <NavLink
                to="/packages"
                className="relative group px-6 py-2.5 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25"
              >
                <span className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                <span className="relative z-10 flex items-center space-x-2">
                  <Sparkles className="w-4 h-4" />
                  <span>Get Started</span>
                </span>
              </NavLink>
            </motion.div>
          </div>

          {/* Enhanced Mobile Menu Button */}
          <motion.button
            className="lg:hidden relative group p-3 rounded-2xl bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-400 border border-white/10 backdrop-blur-sm shadow-lg hover:shadow-blue-500/30 transition-all duration-500"
            onClick={() => setIsOpen(!isOpen)}
            aria-label="Toggle menu"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {/* Button glow effect */}
            <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/30 to-purple-500/30 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <div className="relative z-10">
              <AnimatePresence mode="wait">
                {isOpen ? (
                  <motion.div
                    key="close"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <X size={24} className="text-white" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ rotate: 90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Menu size={24} className="text-blue-400 group-hover:text-white transition-colors duration-300" />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.button>
        </div>

        {/* Modern Mobile Menu */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0, y: -20 }}
              animate={{ height: 'auto', opacity: 1, y: 0 }}
              exit={{ height: 0, opacity: 0, y: -20 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              className="lg:hidden overflow-hidden"
            >
              {/* Mobile menu background with glassmorphism */}
              <div className="relative mt-4 mx-4 rounded-2xl bg-slate-900/95 backdrop-blur-xl border border-white/10 shadow-2xl">
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 rounded-2xl"></div>

                <div className="relative z-10 p-6 space-y-2">
                  {[
                    { to: "/", text: "Home", icon: "🏠" },
                    { to: "/features", text: "Features", icon: "⚡" },
                    { to: "/packages", text: "Packages", icon: "📦" },
                    { to: "/faq", text: "FAQ", icon: "❓" },
                    { to: "/contact", text: "Contact", icon: "📞" }
                  ].map((link, index) => (
                    <motion.div
                      key={link.to}
                      initial={{ x: -30, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: index * 0.1, duration: 0.3 }}
                    >
                      <NavLink
                        to={link.to}
                        className={({ isActive }) =>
                          `group relative flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ${
                            isActive
                              ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border border-blue-400/30'
                              : 'text-slate-300 hover:text-white hover:bg-white/10'
                          }`
                        }
                        onClick={() => setIsOpen(false)}
                      >
                        {({ isActive }) => (
                          <>
                            {/* Background glow for active state */}
                            {isActive && (
                              <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl blur"></div>
                            )}

                            <span className="relative z-10 text-lg">{link.icon}</span>
                            <span className="relative z-10 font-medium">{link.text}</span>

                            {/* Arrow indicator */}
                            <div className="relative z-10 ml-auto">
                              <motion.div
                                animate={{ x: isActive ? 4 : 0 }}
                                transition={{ duration: 0.2 }}
                              >
                                <svg className="w-4 h-4 opacity-50 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                              </motion.div>
                            </div>
                          </>
                        )}
                      </NavLink>
                    </motion.div>
                  ))}

                  {/* Mobile CTA Button */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.6, duration: 0.3 }}
                    className="pt-4 mt-4 border-t border-white/10"
                  >
                    <NavLink
                      to="/packages"
                      className="group relative flex items-center justify-center space-x-2 w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25"
                      onClick={() => setIsOpen(false)}
                    >
                      <span className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                      <Sparkles className="w-5 h-5 relative z-10" />
                      <span className="relative z-10">Get Started</span>
                    </NavLink>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.nav>
  );
};

export default Navbar;
